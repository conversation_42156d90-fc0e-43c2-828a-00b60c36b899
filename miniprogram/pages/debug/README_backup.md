# 调试工具页面（简化版）

这是一个精简而实用的调试工具页面，为开发者提供核心的应用调试和诊断功能。

## 功能特性

### 🔧 激活调试模式
- 在个人页面连续点击"关于应用"5次即可激活调试入口
- 激活后会在个人页面底部显示调试工具入口
- 调试入口具有醒目的红色渐变样式和脉冲动画

### 📱 系统信息
- 设备型号、系统版本、微信版本
- 基础库版本、屏幕尺寸
- 内存使用情况（已用/总计/限制）
- 应用启动时间性能监控
- 小程序AppID和线上版本号
- 运行环境检测（开发版/体验版/正式版）
- 网络状态显示

### 💾 存储信息
- 存储空间使用情况
- 存储项数量和键名列表
- 实时刷新存储状态

### 📊 数据管理
- 查看完整用户数据结构
- 导出数据到剪贴板
- 生成测试数据
- 清空所有数据（危险操作，需要确认）
- 显示工作履历和时间段数量

### ⚙️ 管理器状态
- 查看所有管理器的运行状态
- 显示管理器初始化情况
- 管理器详细信息查看

### 🧪 功能测试
- **数据保存测试**：测试数据持久化功能
- **数据加载测试**：测试数据读取功能
- **数据压缩测试**：测试Deflate压缩算法效果
- **云函数测试**：测试云端API连接
- **通知测试**：测试Toast通知功能
- **性能测试**：测试计算密集型操作性能

### 📝 日志系统
- 实时捕获console.log、console.error、console.warn
- 日志分类显示（信息、警告、错误）
- 日志导出功能
- 日志清空功能
- 最多保留100条日志记录

### 🧭 页面路由调试
- **页面栈信息**：查看当前页面栈深度和层级
- **当前页面详情**：显示当前页面路由、参数等信息
- **页面跳转测试**：快速测试各种页面跳转
- **页面栈管理**：清空页面栈、重置导航状态

### 🗄️ 缓存管理
- **缓存统计**：显示缓存项数量、总大小
- **缓存列表**：查看所有存储项的详细信息
- **缓存查看**：查看单个缓存项的完整内容
- **缓存清理**：删除特定缓存项或清空所有缓存
- **缓存分析**：按大小排序，快速找到占用空间大的项

### 🔐 权限状态检查
- **权限监控**：检查所有小程序权限的授权状态
- **权限申请**：一键申请未授权的权限
- **权限管理**：快速跳转到系统设置页面
- **权限状态**：实时显示权限授权情况（已授权/已拒绝/未询问）

### 🔄 小程序更新检测
- **更新检查**：检测是否有新版本可用
- **版本信息**：显示当前版本和环境信息
- **更新管理**：处理更新下载和应用
- **强制重启**：重启应用以应用更新

### ⚠️ 错误监控增强
- **全局错误捕获**：自动捕获小程序错误、Promise拒绝、内存警告
- **错误统计**：显示错误数量和类型分布
- **错误详情**：查看错误的详细信息和堆栈
- **错误报告**：导出完整的错误报告
- **测试错误**：触发各种类型的测试错误

### 🗜️ 数据压缩分析
- **用户数据压缩测试**：分析真实用户数据的压缩效果
- **自定义数据压缩**：测试不同类型数据的压缩性能
- **压缩性能基准**：多种数据大小的压缩性能对比
- **压缩历史记录**：保存和查看历史压缩测试结果
- **详细压缩指标**：压缩率、耗时、效率等全面分析

### ⚡ 性能分析
- **综合性能评分**：CPU、内存、存储、渲染的综合评估
- **CPU性能测试**：计算密集型任务和数组处理性能
- **内存使用分析**：内存占用和使用效率分析
- **存储I/O测试**：读写速度和存储性能测试
- **渲染性能测试**：页面渲染速度和帧率分析
- **性能历史追踪**：保存和对比历史性能数据

### 🌐 网络诊断
- **网络类型检测**：WiFi、4G、5G等网络类型识别
- **网络延迟测试**：多服务器延迟测试和丢包率分析
- **网络速度测试**：上传下载速度测试
- **DNS解析测试**：域名解析速度和成功率
- **连接稳定性测试**：网络连接稳定性评估
- **快速网络检查**：一键快速网络连通性检查

### 🧠 内存监控
- **实时内存监控**：每2秒采集一次内存使用情况
- **内存使用率分析**：显示内存使用百分比和趋势
- **内存警告系统**：自动检测内存使用率过高或增长过快
- **内存历史记录**：保存最近50次内存采样数据
- **内存分析报告**：生成详细的内存使用统计报告
- **内存压力测试**：创建大量数据测试内存处理能力

### 👂 事件监听器调试
- **页面生命周期监听**：监听onShow、onHide、onLoad、onUnload事件
- **应用生命周期监听**：监听应用显示和隐藏事件
- **事件统计分析**：统计各种事件的触发频次
- **事件历史记录**：记录最近100个事件的详细信息
- **测试事件触发**：模拟各种事件进行测试
- **事件监听开关**：可以随时开启或关闭事件监听

### 📁 文件系统调试
- **文件系统扫描**：扫描用户目录下的所有文件
- **文件类型分析**：统计不同类型文件的数量和大小
- **文件大小统计**：显示总文件数量和占用空间
- **临时文件清理**：自动清理日志文件和临时文件
- **测试文件创建**：创建各种类型的测试文件
- **文件详细信息**：显示文件路径、大小、修改时间等

### 🛠️ 开发工具
- **重置应用**：清空所有数据并重启应用
- **模拟错误**：模拟各种类型的错误（JS错误、网络错误、数据错误、权限错误）
- **强制同步**：手动触发数据同步
- **调试模式开关**：切换应用调试模式
- **自定义命令**：执行自定义调试命令

## 使用方法

### 激活调试模式
1. 打开个人页面
2. 连续快速点击"关于应用"5次
3. 看到"调试模式已激活"提示
4. 在页面底部会出现红色的"调试工具"入口

### 查看系统信息
1. 进入调试页面
2. 系统信息模块默认展开
3. 可以查看设备、系统、应用相关信息
4. 点击"查看应用详情"获取完整应用信息
5. 点击"API兼容性"检查当前基础库支持的API
6. 点击"网络状态"检查当前网络连接情况

### 数据调试
1. 展开"数据管理"模块
2. 点击"查看用户数据"可以查看完整的数据结构
3. 点击"导出数据"可以将数据复制到剪贴板
4. 点击"生成测试数据"可以快速创建测试用的工作履历

### 页面路由调试
1. 展开"页面路由"模块
2. 查看当前页面栈信息
3. 点击"页面详情"查看当前页面的详细信息
4. 使用"测试跳转"快速测试页面导航

### 缓存管理
1. 展开"缓存管理"模块
2. 查看所有缓存项的列表
3. 点击"查看"按钮查看缓存项内容
4. 点击"删除"按钮删除特定缓存项

### 权限检查
1. 展开"权限状态"模块
2. 点击"检查权限"获取最新权限状态
3. 对于未授权的权限，点击"申请"按钮进行授权
4. 点击"打开设置"跳转到系统设置页面

### 更新检测
1. 展开"小程序更新"模块
2. 点击"检查更新"查看是否有新版本
3. 查看版本信息和更新状态

### 数据压缩分析
1. 展开"数据压缩分析"模块
2. 点击"用户数据压缩"测试真实用户数据压缩效果
3. 点击"自定义数据压缩"测试不同类型数据
4. 点击"压缩性能基准"进行全面性能测试
5. 查看压缩率、耗时、效率等详细指标

### 性能分析
1. 展开"性能分析"模块
2. 点击"性能分析"执行综合性能测试
3. 查看CPU、内存、存储、渲染各项评分
4. 点击"查看历史"对比历史性能数据

### 网络诊断
1. 展开"网络诊断"模块
2. 点击"完整诊断"执行全面网络测试
3. 点击"快速检查"进行简单连通性测试
4. 查看延迟、速度、稳定性等网络指标

### 错误监控
1. 展开"错误监控"模块
2. 查看捕获到的错误列表
3. 点击错误项查看详细信息
4. 使用"触发测试错误"测试错误捕获功能

### 功能测试
1. 展开"功能测试"模块
2. 选择要测试的功能
3. 测试结果会显示在下方的结果区域

### 日志查看
1. 展开"日志查看"模块
2. 实时查看应用运行日志
3. 可以导出或清空日志

## 安全注意事项

⚠️ **危险操作**
- "清空所有数据"会永久删除用户数据，需要输入确认文本
- "重置应用"会清空所有数据并重启应用
- "自定义命令"可以执行任意JavaScript代码，请谨慎使用

⚠️ **生产环境**
- 调试功能仅用于开发和测试
- 生产环境中建议禁用或限制调试功能的访问

## 技术实现

### 应用信息获取
使用 `wx.getAccountInfoSync()` 获取准确的应用信息：
```javascript
const accountInfo = wx.getAccountInfoSync()
const miniProgram = accountInfo.miniProgram
// 获取 appId、version、envVersion 等信息
```

### 环境版本检测
自动识别当前运行环境：
- `develop`: 开发版（开发者工具）
- `trial`: 体验版（体验版二维码）
- `release`: 正式版（线上发布版本）

### 性能监控
使用 `wx.getPerformance()` 获取性能数据：
```javascript
const performance = wx.getPerformance()
const memory = performance.memory // 内存使用情况
const timing = performance.timing // 启动时间信息
```

### 日志捕获
通过重写console方法实现日志的自动捕获：
```javascript
console.log = (...args) => {
  originalLog.apply(console, args)
  this.addLog('info', args.join(' '))
}
```

### API兼容性检查
检测当前基础库版本支持的API：
```javascript
const compatibility = {
  getAccountInfoSync: !!wx.getAccountInfoSync,
  getPerformance: !!wx.getPerformance,
  // ... 更多API检查
}
```

### 网络状态检测
使用 `wx.getNetworkType()` 获取网络连接状态。

### 数据压缩测试
利用存储管理器的Deflate压缩算法测试数据压缩效果。

### 管理器状态检测
通过检查管理器实例的存在性来判断管理器状态。

## 扩展功能

可以根据需要添加更多调试功能：
- 网络请求监控
- 页面性能分析
- 内存泄漏检测
- 用户行为追踪
- A/B测试工具

## 版本历史

- v1.0.0: 初始版本，包含基础调试功能
- 支持系统信息、存储信息、数据管理、功能测试等模块
