/**
 * 时间段状态管理测试
 * 用于验证时间段切换时的自动处理逻辑
 */

// 模拟测试环境
const mockApp = {
  getDataManager: () => ({
    getCurrentWork: () => ({
      id: 'test-work-1',
      name: '测试工作'
    }),
    getDayData: (workId, date) => ({
      segments: [
        {
          id: 0,
          start: 540,  // 09:00
          end: 660,    // 11:00
          type: 'work',
          income: 100
        },
        {
          id: 1,
          start: 720,  // 12:00
          end: 750,    // 12:30 (当前测试时间段)
          type: 'work',
          income: 50
        },
        {
          id: 2,
          start: 780,  // 13:00
          end: 1020,   // 17:00
          type: 'work',
          income: 200
        }
      ]
    }),
    getCurrentFishingState: () => ({
      workId: 'test-work-1',
      date: '2025-08-03',
      startTime: new Date().toISOString(),
      startMinutes: 720, // 12:00开始摸鱼
      workSegment: {
        id: 1,
        start: 720,  // 12:00
        end: 750,    // 12:30
        type: 'work',
        income: 50
      },
      remark: '测试摸鱼',
      isActive: true
    })
  })
}

// 设置全局app
global.getApp = () => mockApp

// 导入要测试的模块
const { TimeSegmentStatusManager } = require('../core/managers/time-segment-status-manager')
const { SimpleRealTimeIncome } = require('../utils/real-time-income')

/**
 * 测试时间段状态检测
 */
function testTimeSegmentStatusDetection() {
  console.log('=== 测试时间段状态检测 ===')
  
  const statusManager = new TimeSegmentStatusManager()
  
  // 模拟当前时间为12:25 (745分钟)
  const mockTime = new Date()
  mockTime.setHours(12, 25, 0, 0)
  
  const segments = [
    { id: 0, start: 540, end: 660, type: 'work' },  // 09:00-11:00
    { id: 1, start: 720, end: 750, type: 'work' },  // 12:00-12:30
    { id: 2, start: 780, end: 1020, type: 'work' }  // 13:00-17:00
  ]
  
  const result = statusManager.findCurrentSegment(segments, mockTime)
  
  console.log('当前时间:', mockTime.toTimeString())
  console.log('找到的时间段:', result)
  
  if (result.segment && result.segment.id === 1) {
    console.log('✅ 时间段检测正确')
  } else {
    console.log('❌ 时间段检测错误')
  }
}

/**
 * 测试摸鱼自动结束逻辑
 */
function testFishingAutoEnd() {
  console.log('\n=== 测试摸鱼自动结束逻辑 ===')
  
  const realTimeIncome = new SimpleRealTimeIncome()
  let autoEndTriggered = false
  
  // 设置自动结束回调
  realTimeIncome.autoEndFishingCallback = (fishingState, endMinutes) => {
    console.log('自动结束摸鱼触发:', fishingState, endMinutes)
    autoEndTriggered = true
    // 模拟结束摸鱼
    fishingState.isActive = false
  }
  
  // 模拟摸鱼状态
  const fishingState = {
    workId: 'test-work-1',
    date: '2025-08-03',
    startMinutes: 720, // 12:00开始摸鱼
    workSegment: {
      start: 720,  // 12:00
      end: 750,    // 12:30
      type: 'work'
    },
    isActive: true
  }
  
  // 模拟当前时间为12:31 (751分钟) - 超出工作时间段
  const mockTime = new Date()
  mockTime.setHours(12, 31, 0, 0)
  
  console.log('摸鱼开始时间: 12:00')
  console.log('工作时间段结束: 12:30')
  console.log('当前时间:', mockTime.toTimeString())
  
  // 检查自动结束
  realTimeIncome.checkFishingAutoEnd(fishingState, mockTime, {})
  
  if (autoEndTriggered) {
    console.log('✅ 摸鱼自动结束逻辑正确')
  } else {
    console.log('❌ 摸鱼自动结束逻辑错误')
  }
  
  // 测试防重复触发
  console.log('\n--- 测试防重复触发 ---')
  autoEndTriggered = false
  
  // 再次调用，应该不会触发
  realTimeIncome.checkFishingAutoEnd(fishingState, mockTime, {})
  
  if (!autoEndTriggered) {
    console.log('✅ 防重复触发逻辑正确')
  } else {
    console.log('❌ 防重复触发逻辑错误')
  }
}

/**
 * 测试时间段切换事件
 */
function testTimeSegmentChangeEvents() {
  console.log('\n=== 测试时间段切换事件 ===')
  
  const statusManager = new TimeSegmentStatusManager()
  let eventReceived = null
  
  // 添加监听器
  statusManager.addListener((eventData) => {
    eventReceived = eventData
    console.log('收到时间段变化事件:', eventData.event, eventData.oldStatus, '->', eventData.newStatus)
  })
  
  // 模拟状态变化：从工作状态到无时间段状态
  statusManager.updateStatus('no_segment', null, -1)
  
  if (eventReceived && eventReceived.event === 'enter_no_segment') {
    console.log('✅ 时间段切换事件正确')
  } else {
    console.log('❌ 时间段切换事件错误')
  }
}

/**
 * 运行所有测试
 */
function runAllTests() {
  console.log('开始运行时间段状态管理测试...\n')
  
  try {
    testTimeSegmentStatusDetection()
    testFishingAutoEnd()
    testTimeSegmentChangeEvents()
    
    console.log('\n=== 测试完成 ===')
    console.log('所有测试已运行完毕，请检查上述结果')
    
  } catch (error) {
    console.error('测试运行失败:', error)
  }
}

// 如果直接运行此文件，执行测试
if (typeof module !== 'undefined' && require.main === module) {
  runAllTests()
}

module.exports = {
  testTimeSegmentStatusDetection,
  testFishingAutoEnd,
  testTimeSegmentChangeEvents,
  runAllTests
}
