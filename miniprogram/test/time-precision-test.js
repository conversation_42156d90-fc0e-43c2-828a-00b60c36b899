/**
 * 时间精度测试
 * 验证工作时间段检测的秒级精度修复
 */

// 模拟测试环境
const mockSegments = [
  {
    id: 0,
    start: 540,  // 09:00
    end: 660,    // 11:00
    type: 'work',
    income: 100
  },
  {
    id: 1,
    start: 720,  // 12:00
    end: 826,    // 13:46 (测试的关键时间点)
    type: 'work',
    income: 50
  },
  {
    id: 2,
    start: 840,  // 14:00
    end: 1020,   // 17:00
    type: 'work',
    income: 200
  }
]

/**
 * 测试工作时间段检测的精度
 */
function testWorkTimeDetectionPrecision() {
  console.log('=== 测试工作时间段检测精度 ===')
  
  // 模拟仪表盘1的checkIsInWorkTime方法（简化版）
  function checkIsInWorkTime(now, segments) {
    // 使用秒级精度
    const currentSeconds = now.getHours() * 3600 + now.getMinutes() * 60 + now.getSeconds()
    
    const inWorkSegment = segments.find(segment => {
      if (segment.type === 'rest') {
        return false
      }
      
      const segmentStartSeconds = segment.start * 60
      const segmentEndSeconds = segment.end * 60
      return currentSeconds >= segmentStartSeconds && currentSeconds < segmentEndSeconds
    })
    
    return !!inWorkSegment
  }
  
  // 测试关键时间点：13:46:00 (工作时间段结束时间)
  const testTime1 = new Date()
  testTime1.setHours(13, 45, 59, 0) // 13:45:59 - 应该还在工作时间内

  const testTime2 = new Date()
  testTime2.setHours(13, 46, 0, 0) // 13:46:00 - 应该已经离开工作时间（结束时间不包含在内）

  const testTime3 = new Date()
  testTime3.setHours(13, 47, 0, 0) // 13:47:00 - 应该已经离开工作时间
  
  console.log('测试时间段: 12:00-13:46 (720-826分钟)')
  
  const result1 = checkIsInWorkTime(testTime1, mockSegments)
  const result2 = checkIsInWorkTime(testTime2, mockSegments)
  const result3 = checkIsInWorkTime(testTime3, mockSegments)
  
  console.log(`13:45:59 - 在工作时间内: ${result1} (期望: true)`)
  console.log(`13:46:00 - 在工作时间内: ${result2} (期望: false)`)
  console.log(`13:47:00 - 在工作时间内: ${result3} (期望: false)`)

  // 验证结果
  if (result1 === true && result2 === false && result3 === false) {
    console.log('✅ 工作时间段检测精度测试通过')
  } else {
    console.log('❌ 工作时间段检测精度测试失败')
  }
  
  return {
    passed: result1 === true && result2 === false && result3 === false,
    results: { result1, result2, result3 }
  }
}

/**
 * 测试时间段状态管理器的精度
 */
function testTimeSegmentStatusManagerPrecision() {
  console.log('\n=== 测试时间段状态管理器精度 ===')
  
  // 模拟时间段状态管理器的findCurrentSegment方法（简化版）
  function findCurrentSegment(segments, currentTime) {
    const currentSeconds = currentTime.getHours() * 3600 + currentTime.getMinutes() * 60 + currentTime.getSeconds()
    
    for (let i = 0; i < segments.length; i++) {
      const segment = segments[i]
      const segmentStartSeconds = segment.start * 60
      const segmentEndSeconds = segment.end * 60
      
      if (currentSeconds >= segmentStartSeconds && currentSeconds < segmentEndSeconds) {
        return { segment, index: i }
      }
    }
    
    return { segment: null, index: -1 }
  }
  
  // 测试关键时间点
  const testTime1 = new Date()
  testTime1.setHours(13, 45, 59, 0) // 13:45:59

  const testTime2 = new Date()
  testTime2.setHours(13, 46, 0, 0) // 13:46:00

  const testTime3 = new Date()
  testTime3.setHours(13, 47, 0, 0) // 13:47:00
  
  const result1 = findCurrentSegment(mockSegments, testTime1)
  const result2 = findCurrentSegment(mockSegments, testTime2)
  const result3 = findCurrentSegment(mockSegments, testTime3)
  
  console.log(`13:45:59 - 找到时间段: ${result1.segment ? result1.segment.id : 'null'} (期望: 1)`)
  console.log(`13:46:00 - 找到时间段: ${result2.segment ? result2.segment.id : 'null'} (期望: null)`)
  console.log(`13:47:00 - 找到时间段: ${result3.segment ? result3.segment.id : 'null'} (期望: null)`)

  // 验证结果
  const passed = result1.segment && result1.segment.id === 1 &&
                 result2.segment === null &&
                 result3.segment === null
  
  if (passed) {
    console.log('✅ 时间段状态管理器精度测试通过')
  } else {
    console.log('❌ 时间段状态管理器精度测试失败')
  }
  
  return { passed, results: { result1, result2, result3 } }
}

/**
 * 测试倒计时计算的精度
 */
function testCountdownCalculationPrecision() {
  console.log('\n=== 测试倒计时计算精度 ===')
  
  // 模拟倒计时计算（简化版）
  function calculateCountdown(segments, currentTime) {
    const currentSeconds = currentTime.getHours() * 3600 + currentTime.getMinutes() * 60 + currentTime.getSeconds()
    
    for (let i = 0; i < segments.length; i++) {
      const segment = segments[i]
      const segmentStartSeconds = segment.start * 60
      const segmentEndSeconds = segment.end * 60
      
      if (currentSeconds >= segmentStartSeconds && currentSeconds < segmentEndSeconds) {
        // 在时间段内，计算到结束的倒计时
        const remainingSeconds = segmentEndSeconds - currentSeconds
        return {
          inSegment: true,
          remainingSeconds: remainingSeconds,
          segmentId: segment.id
        }
      }
    }
    
    return { inSegment: false, remainingSeconds: 0, segmentId: null }
  }
  
  // 测试关键时间点
  const testTime1 = new Date()
  testTime1.setHours(13, 45, 30, 0) // 13:45:30 - 距离结束还有30秒
  
  const testTime2 = new Date()
  testTime2.setHours(13, 45, 59, 0) // 13:45:59 - 距离结束还有1秒
  
  const testTime3 = new Date()
  testTime3.setHours(13, 46, 0, 0) // 13:46:00 - 刚好结束（不在时间段内）
  
  const result1 = calculateCountdown(mockSegments, testTime1)
  const result2 = calculateCountdown(mockSegments, testTime2)
  const result3 = calculateCountdown(mockSegments, testTime3)
  
  console.log(`13:45:30 - 剩余秒数: ${result1.remainingSeconds} (期望: 30)`)
  console.log(`13:45:59 - 剩余秒数: ${result2.remainingSeconds} (期望: 1)`)
  console.log(`13:46:00 - 在时间段内: ${result3.inSegment} (期望: false)`)
  
  // 验证结果
  const passed = result1.remainingSeconds === 30 &&
                 result2.remainingSeconds === 1 &&
                 result3.inSegment === false
  
  if (passed) {
    console.log('✅ 倒计时计算精度测试通过')
  } else {
    console.log('❌ 倒计时计算精度测试失败')
  }
  
  return { passed, results: { result1, result2, result3 } }
}

/**
 * 运行所有测试
 */
function runAllTests() {
  console.log('开始运行时间精度测试...\n')
  
  const test1 = testWorkTimeDetectionPrecision()
  const test2 = testTimeSegmentStatusManagerPrecision()
  const test3 = testCountdownCalculationPrecision()
  
  console.log('\n=== 测试总结 ===')
  console.log(`工作时间段检测精度: ${test1.passed ? '✅ 通过' : '❌ 失败'}`)
  console.log(`时间段状态管理器精度: ${test2.passed ? '✅ 通过' : '❌ 失败'}`)
  console.log(`倒计时计算精度: ${test3.passed ? '✅ 通过' : '❌ 失败'}`)
  
  const allPassed = test1.passed && test2.passed && test3.passed
  console.log(`\n总体结果: ${allPassed ? '✅ 所有测试通过' : '❌ 部分测试失败'}`)
  
  return {
    allPassed,
    individual: { test1, test2, test3 }
  }
}

// 如果直接运行此文件，执行测试
if (typeof module !== 'undefined' && require.main === module) {
  runAllTests()
}

module.exports = {
  testWorkTimeDetectionPrecision,
  testTimeSegmentStatusManagerPrecision,
  testCountdownCalculationPrecision,
  runAllTests
}
