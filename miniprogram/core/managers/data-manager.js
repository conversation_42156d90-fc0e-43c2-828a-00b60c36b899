/**
 * 全局数据管理器 - 重构版本
 * 作为协调器整合各个专门的管理器
 *
 * 功能特性：
 * - 单例模式确保全局唯一实例
 * - 内存中缓存数据，提升访问性能
 * - 防抖保存机制，避免频繁写入存储
 * - 协调各个专门管理器的工作
 * - 统一的数据结构 USER_DATA
 */

const { getStorageManager } = require('./storage-manager.js')
const { getWorkManager } = require('./work-manager.js')
const { getSettingsManager } = require('./settings-manager.js')
const { getTimeTrackingManager } = require('./time-tracking-manager.js')
const { getFishingManager } = require('./fishing-manager.js')
const { getTimeSegmentStatusManager } = require('./time-segment-status-manager.js')
const { formatDateKey } = require('../../utils/helpers/time-utils.js')
const { normalizeTime } = require('../../utils/time-utils.js')

/**
 * 全局数据管理器类
 * 作为协调器整合各个专门的管理器
 */
class DataManager {
  constructor() {
    if (DataManager.instance) {
      return DataManager.instance
    }

    // 内存中的数据缓存
    this.userData = null

    // 防抖保存定时器
    this.saveTimer = null

    // 保存防抖延迟（毫秒）
    this.saveDelay = 1000

    // 数据变化监听器数组
    this.changeListeners = []

    // 数据加载状态
    this.isLoaded = false
    this.loadPromise = null

    // 性能优化缓存
    this.cache = {
      currentWork: null,
      workList: null,
      lastCacheTime: 0,
      cacheTimeout: 5000 // 5秒缓存超时
    }

    // 各个专门的管理器实例
    this.storageManager = getStorageManager()
    this.workManager = getWorkManager()
    this.settingsManager = getSettingsManager()
    this.timeTrackingManager = getTimeTrackingManager()
    this.fishingManager = getFishingManager()
    this.timeSegmentStatusManager = getTimeSegmentStatusManager()

    // 数据将在 loadData 时初始化，不在构造函数中初始化
    // this.initializeData() // 移除自动初始化

    DataManager.instance = this
  }

  /**
   * 获取单例实例
   * @returns {DataManager} 数据管理器实例
   */
  static getInstance() {
    if (!DataManager.instance) {
      DataManager.instance = new DataManager()
    }
    return DataManager.instance
  }

  /**
   * 初始化数据结构
   * 使用各个管理器来初始化数据
   */
  initializeData() {
    const now = normalizeTime()

    this.userData = {
      // 数据版本，用于未来的数据迁移
      version: '0.3.0',

      // 最后修改时间（统一使用 lastModified，秒级精度）
      lastModified: now,

      // 工作履历数据 - 使用WorkManager初始化
      workHistory: this.workManager.initializeWorkHistory(),

      // 用户设置 - 使用SettingsManager初始化
      settings: this.settingsManager.initializeSettings(),

      // 创建时间（秒级精度）
      createTime: now
    }
  }

  /**
   * 从存储加载数据
   * 使用StorageManager进行数据加载
   */
  async loadData() {
    // 如果已经有加载Promise在执行，直接返回
    if (this.loadPromise) {
      return this.loadPromise
    }

    this.loadPromise = this._performLoad()
    try {
      await this.loadPromise
      this.isLoaded = true
    } finally {
      this.loadPromise = null
    }
  }

  /**
   * 执行实际的数据加载
   */
  async _performLoad() {
    try {
      console.log('开始加载用户数据...')

      const loadedData = await this.storageManager.loadData()

      if (!loadedData) {
        console.log('未找到存储数据，创建初始化数据')
        this.initializeData() // 只在没有存储数据时才初始化
        this.notifyChange()
        return
      }

      // 直接使用加载的数据，保持原有的 lastModified 时间
      this.userData = this.storageManager.processLoadedData(loadedData)

      console.log('用户数据加载成功，lastModified:', this.userData.lastModified)
      this.notifyChange()

    } catch (error) {
      console.error('加载用户数据失败:', error)
      console.log('使用初始化数据')
      this.initializeData()
      this.notifyChange()
    }
  }

  /**
   * 确保数据已加载
   * @returns {Promise} 加载完成的Promise
   */
  async ensureLoaded() {
    if (this.isLoaded) {
      return
    }

    if (this.loadPromise) {
      await this.loadPromise
      return
    }

    await this.loadData()
  }



  /**
   * 保存数据到存储
   * 使用防抖机制，避免频繁保存
   * @param {boolean} immediate - 是否立即保存，跳过防抖
   */
  saveData(immediate = false) {
    if (immediate) {
      this.performSave()
      return
    }

    // 清除之前的定时器
    if (this.saveTimer) {
      clearTimeout(this.saveTimer)
    }

    // 设置新的防抖定时器
    this.saveTimer = setTimeout(() => {
      this.performSave()
    }, this.saveDelay)
  }

  /**
   * 执行实际的保存操作
   * 使用StorageManager进行保存
   */
  performSave() {
    try {
      console.log('开始保存用户数据...')

      // 更新最后修改时间（使用标准化的秒级精度时间）
      const oldLastModified = this.userData.lastModified
      this.userData.lastModified = normalizeTime()
      console.log('更新 lastModified:', oldLastModified, '->', this.userData.lastModified)

      // 使用StorageManager保存数据
      const success = this.storageManager.saveData(this.userData)

      if (!success) {
        throw new Error('数据保存失败')
      }

    } catch (error) {
      console.error('保存用户数据失败:', error)
      throw new Error('数据保存失败')
    }
  }

  /**
   * 添加数据变化监听器
   * @param {Function} listener - 监听器函数
   */
  addChangeListener(listener) {
    if (typeof listener === 'function') {
      this.changeListeners.push(listener)
    }
  }

  /**
   * 移除数据变化监听器
   * @param {Function} listener - 要移除的监听器函数
   */
  removeChangeListener(listener) {
    const index = this.changeListeners.indexOf(listener)
    if (index > -1) {
      this.changeListeners.splice(index, 1)
    }
  }

  /**
   * 清理缓存
   */
  clearCache() {
    this.cache.currentWork = null
    this.cache.workList = null
    this.cache.lastCacheTime = 0
  }

  /**
   * 检查缓存是否有效
   * @returns {boolean} 缓存是否有效
   */
  isCacheValid() {
    const now = Date.now()
    return (now - this.cache.lastCacheTime) < this.cache.cacheTimeout
  }

  /**
   * 通知所有监听器数据已变化
   */
  notifyChange() {
    // 清理缓存，因为数据已变化
    this.clearCache()

    this.changeListeners.forEach(listener => {
      try {
        listener(this.userData)
      } catch (error) {
        console.error('数据变化监听器执行失败:', error)
      }
    })
  }

  /**
   * 获取完整的用户数据
   * @returns {Object} 用户数据对象
   */
  getUserData() {
    return this.userData
  }

  /**
   * 获取用户数据（不触发自动初始化）
   * 用于同步检查时避免不必要的数据初始化
   * @returns {Object|null} 用户数据对象或null
   */
  getUserDataWithoutInit() {
    // 总是从存储中重新检查，确保数据的准确性
    try {
      const loadedData = this.storageManager.loadDataSync()
      if (loadedData) {
        // 如果存储中有数据，更新内存中的数据
        this.userData = this.storageManager.processLoadedData(loadedData)
        this.isLoaded = true
        return this.userData
      } else {
        // 如果存储中没有数据，清空内存中的数据
        this.userData = null
        this.isLoaded = false
        return null
      }
    } catch (error) {
      console.log('无法加载存储数据:', error)
      return null
    }
  }

  /**
   * 获取所有工作履历
   * @returns {Object} 工作履历对象(Hashmap)
   */
  getWorkHistory() {
    return this.userData.workHistory || {}
  }

  /**
   * 获取指定工作履历
   * @param {string} workId - 工作履历ID
   * @returns {Object|null} 工作履历对象或null
   */
  getWork(workId) {
    return this.userData.workHistory[workId] || null
  }

  /**
   * 添加工作履历
   * @param {Object} workData - 工作履历数据
   * @returns {string} 新工作履历的ID
   */
  addWork(workData) {
    const newWork = this.workManager.createWork(workData)
    const workId = newWork.id

    this.userData.workHistory[workId] = newWork

    // 如果是第一个工作履历，设置为当前工作
    if (Object.keys(this.userData.workHistory).length === 1) {
      this.userData.settings.currentWorkId = workId
    }

    this.saveData()
    this.notifyChange()

    return workId
  }

  /**
   * 更新工作履历
   * @param {string} workId - 工作履历ID
   * @param {Object} updateData - 更新的数据
   */
  updateWork(workId, updateData) {
    if (!this.userData.workHistory[workId]) {
      throw new Error('工作履历不存在')
    }

    const existingWork = this.userData.workHistory[workId]
    const updatedWork = this.workManager.updateWork(existingWork, updateData)

    this.userData.workHistory[workId] = updatedWork

    this.saveData()
    this.notifyChange()
  }

  /**
   * 删除工作履历
   * @param {string} workId - 工作履历ID
   */
  deleteWork(workId) {
    if (!this.userData.workHistory[workId]) {
      throw new Error('工作履历不存在')
    }

    delete this.userData.workHistory[workId]

    // 如果删除的是当前工作，需要重新设置当前工作
    if (this.userData.settings.currentWorkId === workId) {
      const remainingWorkIds = Object.keys(this.userData.workHistory)
      this.userData.settings.currentWorkId = remainingWorkIds.length > 0 ? remainingWorkIds[0] : null
    }

    this.saveData()
    this.notifyChange()
  }

  /**
   * 获取当前工作履历（带缓存优化）
   * @returns {Object|null} 当前工作履历对象或null
   */
  getCurrentWork() {
    // 检查缓存是否有效
    if (this.isCacheValid() && this.cache.currentWork !== null) {
      return this.cache.currentWork
    }

    const currentWorkId = this.userData.settings.currentWorkId
    const currentWork = currentWorkId ? this.userData.workHistory[currentWorkId] : null

    // 更新缓存
    this.cache.currentWork = currentWork
    this.cache.lastCacheTime = Date.now()

    return currentWork
  }

  /**
   * 设置当前工作
   * @param {string} workId - 工作履历ID
   * @param {boolean} notify - 是否通知变化，默认为true
   */
  setCurrentWork(workId, notify = true) {
    if (!this.userData.workHistory[workId]) {
      throw new Error('工作履历不存在')
    }

    this.userData.settings.currentWorkId = workId
    this.saveData()

    if (notify) {
      this.notifyChange()
    }
  }

  /**
   * 获取用户设置
   * @returns {Object} 设置对象
   */
  getSettings() {
    return this.userData.settings
  }

  /**
   * 更新用户设置
   * @param {Object} newSettings - 新的设置数据
   */
  updateSettings(newSettings) {
    this.userData.settings = this.settingsManager.mergeSettings(this.userData.settings, newSettings)
    this.saveData()
    this.notifyChange()
  }

  /**
   * 获取当前仪表盘ID
   * @returns {string} 仪表盘ID
   */
  getCurrentDashboard() {
    return this.settingsManager.getCurrentDashboard(this.userData.settings)
  }

  /**
   * 设置当前仪表盘
   * @param {string} dashboardId - 仪表盘ID
   */
  setCurrentDashboard(dashboardId) {
    this.settingsManager.setCurrentDashboard(this.userData.settings, dashboardId)
    this.saveData()
    this.notifyChange()
  }

  /**
   * 获取仪表盘配置
   * @param {string} dashboardId - 仪表盘ID
   * @returns {Object} 仪表盘配置
   */
  getDashboardConfig(dashboardId) {
    return this.settingsManager.getDashboardConfig(this.userData.settings, dashboardId)
  }

  /**
   * 更新仪表盘配置
   * @param {string} dashboardId - 仪表盘ID
   * @param {Object} config - 配置数据
   */
  updateDashboardConfig(dashboardId, config) {
    this.settingsManager.updateDashboardConfig(this.userData.settings, dashboardId, config)
    this.saveData()
    this.notifyChange()
  }

  /**
   * 获取仪表盘设置
   * @returns {Object} 仪表盘设置
   */
  getDashboardSettings() {
    this.settingsManager.ensureDashboardSettings(this.userData.settings)
    return this.userData.settings.dashboard
  }

  /**
   * 更新仪表盘设置
   * @param {Object} settings - 仪表盘设置
   */
  updateDashboardSettings(settings) {
    this.settingsManager.ensureDashboardSettings(this.userData.settings)
    this.userData.settings.dashboard = this.settingsManager.mergeSettings(this.userData.settings.dashboard, settings)
    this.saveData()
    this.notifyChange()
  }

  /**
   * 获取指定工作的时间追踪数据
   * @param {string} workId - 工作履历ID
   * @returns {Object} 时间追踪数据
   */
  getTimeTracking(workId) {
    const work = this.getWork(workId)
    return work ? work.timeTracking || {} : {}
  }



  /**
   * 获取指定日期的时间数据
   * @param {string} workId - 工作履历ID
   * @param {Date} date - 日期
   * @returns {Object} 日期数据
   */
  getDayData(workId, date) {
    const dateKey = formatDateKey(date)
    const timeTracking = this.getTimeTracking(workId)

    let dayData = timeTracking[dateKey]

    // 如果没有数据，创建新的
    if (!dayData) {
      return this.timeTrackingManager.createDayData(date)
    }

    // 确保数据结构完整
    if (!dayData.extraIncomes) dayData.extraIncomes = []
    if (!dayData.deductions) dayData.deductions = []
    if (!dayData.segments) dayData.segments = []
    if (!dayData.fishes) dayData.fishes = []

    // 移除冗余字段的初始化，这些字段现在通过计算获得：
    // - dailyIncome, totalWorkMinutes, hourlyRate, netIncome

    return dayData
  }



  /**
   * 保存指定日期的时间数据
   * @param {string} workId - 工作履历ID
   * @param {Date} date - 日期
   * @param {Object} dayData - 日期数据
   */
  saveDayData(workId, date, dayData) {
    if (!this.userData.workHistory[workId]) {
      throw new Error('工作履历不存在')
    }

    const dateKey = formatDateKey(date)

    if (!this.userData.workHistory[workId].timeTracking) {
      this.userData.workHistory[workId].timeTracking = this.timeTrackingManager.initializeTimeTracking()
    }

    this.userData.workHistory[workId].timeTracking[dateKey] = Object.assign({}, dayData)

    this.saveData()
    this.notifyChange()
  }

  /**
   * 清除所有数据
   */
  clearAllData() {
    try {
      this.storageManager.clearAllData()
      this.initializeData()
      this.notifyChange()
      console.log('所有数据已清除')
    } catch (error) {
      console.error('清除数据失败:', error)
      throw new Error('数据清除失败')
    }
  }

  /**
   * 导出数据
   * @returns {string} JSON格式的数据字符串
   */
  exportData() {
    const exportData = {
      version: this.userData.version,
      exportTime: new Date().toISOString(),
      userData: this.storageManager.prepareDataForSave(this.userData)
    }

    return JSON.stringify(exportData, null, 2)
  }

  /**
   * 导入数据
   * @param {string} jsonData - JSON格式的数据字符串
   */
  importData(jsonData) {
    try {
      const importData = JSON.parse(jsonData)

      if (!importData.userData) {
        throw new Error('导入数据格式无效')
      }

      this.userData = this.storageManager.processLoadedData(importData.userData)
      this.saveData(true) // 立即保存
      this.notifyChange()

      console.log('数据导入成功')
    } catch (error) {
      console.error('数据导入失败:', error)
      throw new Error('数据导入失败: ' + error.message)
    }
  }

  /**
   * 加载用户数据（用于数据同步）
   * @param {Object} userData - 用户数据对象
   * @param {boolean} notifyChange - 是否触发数据变化监听器，默认false
   */
  loadUserData(userData, notifyChange = false) {
    try {
      console.log('开始加载同步的用户数据...')

      if (!userData) {
        console.log('用户数据为空，使用默认数据')
        return
      }

      // 处理并应用用户数据
      this.userData = this.storageManager.processLoadedData(userData)
      this.isLoaded = true

      // 保留原始的lastModified时间戳，直接保存不更新时间戳
      try {
        const success = this.storageManager.saveData(this.userData)
        if (!success) {
          throw new Error('数据保存失败')
        }
      } catch (saveError) {
        console.error('保存同步数据失败:', saveError)
        throw saveError
      }

      // 总是通知页面更新（用于UI刷新），但根据参数决定是否触发数据变化监听器
      this.notifyChange()

      // 如果不需要触发数据变化监听器（避免重复上传），则暂停监听器
      if (!notifyChange) {
        console.log('数据同步加载，暂时跳过上传监听')
      }

      console.log('同步用户数据加载成功')
    } catch (error) {
      console.error('加载同步用户数据失败:', error)
      throw error
    }
  }

  // ==================== 摸鱼相关方法 ====================

  /**
   * 添加摸鱼记录
   * @param {string} workId - 工作履历ID
   * @param {Date} date - 日期
   * @param {Object} fishingData - 摸鱼数据
   * @returns {number} 新摸鱼记录的ID
   */
  addFishing(workId, date, fishingData) {
    const dayData = this.getDayData(workId, date)

    if (!dayData.fishes) {
      dayData.fishes = []
    }

    const updatedFishes = this.timeTrackingManager.addFishing(dayData.fishes, fishingData)
    dayData.fishes = updatedFishes

    this.saveDayData(workId, date, dayData)
    this.notifyChange()

    // 返回新添加的摸鱼记录ID
    return updatedFishes[updatedFishes.length - 1].id
  }

  /**
   * 更新摸鱼记录
   * @param {string} workId - 工作履历ID
   * @param {Date} date - 日期
   * @param {number} fishingId - 摸鱼ID
   * @param {Object} updateData - 更新数据
   */
  updateFishing(workId, date, fishingId, updateData) {
    const dayData = this.getDayData(workId, date)

    if (!dayData.fishes) {
      dayData.fishes = []
    }

    const updatedFishes = this.timeTrackingManager.updateFishing(dayData.fishes, fishingId, updateData)
    dayData.fishes = updatedFishes

    this.saveDayData(workId, date, dayData)
    this.notifyChange()
  }

  /**
   * 删除摸鱼记录
   * @param {string} workId - 工作履历ID
   * @param {Date} date - 日期
   * @param {number} fishingId - 摸鱼ID
   */
  deleteFishing(workId, date, fishingId) {
    const dayData = this.getDayData(workId, date)

    if (!dayData.fishes) {
      return
    }

    const updatedFishes = this.timeTrackingManager.deleteFishing(dayData.fishes, fishingId)
    dayData.fishes = updatedFishes

    this.saveDayData(workId, date, dayData)
    this.notifyChange()
  }

  /**
   * 获取摸鱼记录
   * @param {string} workId - 工作履历ID
   * @param {Date} date - 日期
   * @param {number} fishingId - 摸鱼ID
   * @returns {Object|null} 摸鱼记录
   */
  getFishing(workId, date, fishingId) {
    const dayData = this.getDayData(workId, date)

    if (!dayData.fishes) {
      return null
    }

    return this.timeTrackingManager.getFishing(dayData.fishes, fishingId)
  }

  /**
   * 获取指定日期的所有摸鱼记录
   * @param {string} workId - 工作履历ID
   * @param {Date} date - 日期
   * @returns {Array} 摸鱼记录数组
   */
  getDayFishes(workId, date) {
    const dayData = this.getDayData(workId, date)
    return dayData.fishes || []
  }

  /**
   * 开始摸鱼
   * @param {string} remark - 备注
   * @returns {Object} 操作结果
   */
  startFishing(remark = '') {
    const currentWork = this.getCurrentWork()
    if (!currentWork) {
      return {
        success: false,
        message: '没有当前工作履历'
      }
    }

    const today = new Date()
    const dayData = this.getDayData(currentWork.id, today)

    return this.fishingManager.startFishing(currentWork.id, today, dayData.segments, remark)
  }

  /**
   * 结束摸鱼
   * @returns {Object} 操作结果
   */
  endFishing() {
    return this.fishingManager.endFishing((workId, dateStr, fishingRecord) => {
      const date = new Date(dateStr)
      this.addFishing(workId, date, fishingRecord)
    })
  }

  /**
   * 获取当前摸鱼状态
   * @returns {Object|null} 摸鱼状态
   */
  getCurrentFishingState() {
    return this.fishingManager.getFishingState()
  }

  /**
   * 更新当前摸鱼的备注
   * @param {string} remark - 新的备注
   * @returns {Object} 操作结果
   */
  updateCurrentFishingRemark(remark) {
    try {
      const fishingState = this.fishingManager.getFishingState()

      if (!fishingState || !fishingState.isActive) {
        return {
          success: false,
          message: '当前没有进行中的摸鱼'
        }
      }

      // 更新摸鱼状态中的备注
      fishingState.remark = remark || ''
      this.fishingManager.saveFishingState(fishingState)

      return {
        success: true,
        message: '备注更新成功',
        fishingState: fishingState
      }
    } catch (error) {
      console.error('更新摸鱼备注失败:', error)
      return {
        success: false,
        message: error.message || '更新失败'
      }
    }
  }

  /**
   * 检查并处理过期的摸鱼状态
   * 用于小程序重新打开时恢复摸鱼状态，支持跨日期检查
   * @returns {Object} 处理结果
   */
  checkAndHandleExpiredFishing() {
    const fishingState = this.fishingManager.getFishingState()

    if (!fishingState || !fishingState.isActive) {
      return { hasExpiredFishing: false }
    }

    const now = new Date()
    const fishingStartDate = new Date(fishingState.date)
    const fishingStartTime = new Date(fishingState.startTime)
    const workSegment = fishingState.workSegment

    // 检查是否跨日期
    const isCrossDate = now.toDateString() !== fishingStartDate.toDateString()

    if (isCrossDate) {
      // 跨日期情况：检查摸鱼开始的时间段是否跨日期
      const isWorkSegmentCrossDate = this.isWorkSegmentCrossDate(workSegment)

      if (isWorkSegmentCrossDate) {
        // 工作时间段跨日期，需要检查当前时间是否还在该时间段内
        const result = this.checkCrossDateFishingStatus(fishingState, now)
        return result
      } else {
        // 工作时间段不跨日期，说明摸鱼已经超出了时间段，自动结束
        return this.autoEndFishing(fishingState, workSegment.end, '跨日期自动结束')
      }
    } else {
      // 同一天：使用原有逻辑检查
      return this.checkSameDayFishingStatus(fishingState, now)
    }
  }

  /**
   * 检查工作时间段是否跨日期
   * @param {Object} workSegment - 工作时间段
   * @returns {boolean} 是否跨日期
   */
  isWorkSegmentCrossDate(workSegment) {
    // 如果结束时间小于开始时间，说明跨日期
    return workSegment.end < workSegment.start
  }

  /**
   * 检查跨日期摸鱼状态
   * @param {Object} fishingState - 摸鱼状态
   * @param {Date} now - 当前时间
   * @returns {Object} 检查结果
   */
  checkCrossDateFishingStatus(fishingState, now) {
    const workSegment = fishingState.workSegment
    const currentMinutes = now.getHours() * 60 + now.getMinutes()

    // 跨日期工作时间段的逻辑：
    // 例如：22:00-06:00，表示从22:00到次日06:00
    // 如果当前时间是次日的05:00，应该还在工作时间段内
    // 如果当前时间是次日的07:00，应该已经超出工作时间段

    if (currentMinutes <= workSegment.end) {
      // 当前时间在跨日期工作时间段的结束部分内，继续摸鱼
      return {
        hasExpiredFishing: true,
        autoEnded: false,
        fishingState: fishingState,
        message: '跨日期摸鱼继续进行中'
      }
    } else {
      // 当前时间超出了跨日期工作时间段，自动结束摸鱼
      return this.autoEndFishing(fishingState, workSegment.end, '跨日期工作时间段结束')
    }
  }

  /**
   * 检查同一天的摸鱼状态
   * @param {Object} fishingState - 摸鱼状态
   * @param {Date} now - 当前时间
   * @returns {Object} 检查结果
   */
  checkSameDayFishingStatus(fishingState, now) {
    const currentMinutes = now.getHours() * 60 + now.getMinutes()
    const workSegment = fishingState.workSegment

    // 检查当前时间是否还在摸鱼开始时间所在的工作时间段内
    if (currentMinutes > workSegment.end) {
      // 超出了摸鱼开始时间所在的工作时间段，自动结束摸鱼
      return this.autoEndFishing(fishingState, workSegment.end, '超出工作时间段')
    }

    // 检查当前时间是否还在任何工作时间段内
    const currentWork = this.getCurrentWork()
    if (currentWork) {
      const today = new Date()
      const dayData = this.getDayData(currentWork.id, today)

      const currentWorkSegment = dayData.segments.find(segment => {
        return segment.type !== 'rest' &&
               currentMinutes >= segment.start &&
               currentMinutes <= segment.end
      })

      if (!currentWorkSegment) {
        // 当前不在任何工作时间段内，但还没超出摸鱼开始的工作时间段
        // 保持摸鱼状态，等待进入下一个工作时间段或自动结束
        return {
          hasExpiredFishing: true,
          autoEnded: false,
          fishingState: fishingState,
          message: '当前不在工作时间内，摸鱼状态暂停'
        }
      }
    }

    // 还在有效的摸鱼状态中
    return {
      hasExpiredFishing: true,
      autoEnded: false,
      fishingState: fishingState
    }
  }

  /**
   * 自动结束摸鱼
   * @param {Object} fishingState - 摸鱼状态
   * @param {number} endMinutes - 结束时间（分钟）
   * @param {string} reason - 结束原因
   * @returns {Object} 处理结果
   */
  autoEndFishing(fishingState, endMinutes, reason) {
    const fishingRecord = {
      id: 0,
      start: fishingState.startMinutes,
      end: endMinutes,
      remark: (fishingState.remark || '') + ` (${reason})`
    }

    // 保存摸鱼记录到摸鱼开始的那一天
    const date = new Date(fishingState.date)
    this.addFishing(fishingState.workId, date, fishingRecord)

    // 清除摸鱼状态
    this.fishingManager.clearFishingState()

    return {
      hasExpiredFishing: true,
      autoEnded: true,
      message: `摸鱼已自动结束（${reason}）`,
      fishingRecord: fishingRecord
    }
  }

  /**
   * 启动时间段状态管理
   */
  startTimeSegmentStatusManagement() {
    if (this.timeSegmentStatusManager) {
      this.timeSegmentStatusManager.start()
      console.log('[DataManager] 启动时间段状态管理')
    }

    // 连接时间段状态管理器和摸鱼管理器
    if (this.timeSegmentStatusManager && this.fishingManager) {
      this.timeSegmentStatusManager.addListener((eventData) => {
        this.fishingManager.handleTimeSegmentChange(eventData)
      })
      console.log('[DataManager] 连接时间段状态管理器和摸鱼管理器')
    }
  }

  /**
   * 停止时间段状态管理
   */
  stopTimeSegmentStatusManagement() {
    if (this.timeSegmentStatusManager) {
      this.timeSegmentStatusManager.stop()
      console.log('[DataManager] 停止时间段状态管理')
    }
  }

  /**
   * 获取当前时间段状态
   * @returns {Object} 当前时间段状态信息
   */
  getCurrentTimeSegmentStatus() {
    if (this.timeSegmentStatusManager) {
      return this.timeSegmentStatusManager.getCurrentStatus()
    }
    return {
      status: 'no_segment',
      segment: null,
      segmentIndex: -1,
      isWorking: false,
      isResting: false,
      hasSegment: false
    }
  }

  /**
   * 添加时间段状态变化监听器
   * @param {Function} listener - 监听器函数
   */
  addTimeSegmentStatusListener(listener) {
    if (this.timeSegmentStatusManager) {
      this.timeSegmentStatusManager.addListener(listener)
    }
  }

  /**
   * 移除时间段状态变化监听器
   * @param {Function} listener - 监听器函数
   */
  removeTimeSegmentStatusListener(listener) {
    if (this.timeSegmentStatusManager) {
      this.timeSegmentStatusManager.removeListener(listener)
    }
  }

  /**
   * 添加摸鱼自动结束监听器
   * @param {Function} listener - 监听器函数
   */
  addFishingAutoEndListener(listener) {
    if (this.fishingManager) {
      this.fishingManager.addAutoEndListener(listener)
    }
  }

  /**
   * 移除摸鱼自动结束监听器
   * @param {Function} listener - 监听器函数
   */
  removeFishingAutoEndListener(listener) {
    if (this.fishingManager) {
      this.fishingManager.removeAutoEndListener(listener)
    }
  }
}

// 导出单例实例
let dataManagerInstance = null

function getDataManager() {
  if (!dataManagerInstance) {
    dataManagerInstance = new DataManager()
  }
  return dataManagerInstance
}

module.exports = {
  DataManager,
  getDataManager
}